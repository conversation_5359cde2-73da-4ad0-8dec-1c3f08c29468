import { Typography } from '@bratislava/component-library'
import { useTranslation } from 'next-i18next'
import React, { ChangeEvent, useEffect, useState } from 'react'

import { ArrowLeftIcon, ArrowRightIcon } from '@/src/assets/icons'
import Button from '@/src/components/common/Button/Button'
import Input from '@/src/components/common/Input/Input'
import cn from '@/src/utils/cn'

type InputValue = '' | number

type PaginationWithInputProps = {
  currentPage: number
  totalCount: number
  onPageChange: (value: number) => void
}

/**
 * Figma: https://www.figma.com/design/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=37-1906&t=Ix6vxd23ycmma0c2-4
 * Interaction design inspired by: https://ant.design/components/pagination
 */

const PaginationWithInput = ({
  currentPage,
  totalCount,
  onPageChange: handlePageChange,
}: PaginationWithInputProps) => {
  const { t } = useTranslation()

  // inputValue is detached from currentPage to allow empty input value without changing currentPage
  const [inputValue, setInputValue] = useState<InputValue>(currentPage)

  useEffect(() => {
    setInputValue(currentPage)
  }, [currentPage])

  // TODO handle typing symbols .-e (currently possible, but does no harm)
  const getValidValue = (incomingValue: HTMLInputElement['value']) => {
    let result: InputValue
    const incomingNumberValue = Number(incomingValue)

    if (incomingValue === '') {
      result = ''
    } else if (Number.isNaN(incomingNumberValue)) {
      result = currentPage
    } else if (incomingNumberValue > totalCount) {
      result = totalCount
    } else if (incomingNumberValue < 1) {
      result = 1
    } else {
      result = Number.isInteger(incomingNumberValue)
        ? incomingNumberValue
        : Math.floor(incomingNumberValue)
    }

    return result
  }

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const validValue = getValidValue(event.currentTarget.value)

    if (validValue !== inputValue) {
      setInputValue(validValue)
    }

    if (validValue) {
      handlePageChange(validValue)
    }
  }

  return (
    <nav>
      <div className={cn('flex items-center justify-start gap-4')}>
        <Button
          variant="plain"
          isDisabled={Number(inputValue) < 2}
          onPress={() => handlePageChange(currentPage - 1)}
          aria-label={t('Pagination.aria.goToPreviousPage')}
          icon={<ArrowLeftIcon />}
          className="rounded-full"
        />

        <div className="flex items-center justify-center gap-2">
          <Input
            type="number"
            aria-label={t('Pagination.aria.goToPage', { page: inputValue })}
            value={inputValue}
            onChange={handleChange}
            className={cn(
              'items-center justify-center',
              // Set input width to fit three digits
              '[&_[data-input]]:w-16 [&_[data-input]]:text-center',
            )}
          />

          <div className="flex gap-1">
            <div className="flex size-6 justify-center">
              <Typography variant="p-default">/</Typography>
            </div>
            <Typography variant="p-default">{totalCount}</Typography>
          </div>
        </div>

        <Button
          variant="plain"
          isDisabled={Number(inputValue) >= totalCount || inputValue.toString() === ''}
          onPress={() => handlePageChange(currentPage + 1)}
          aria-label={t('Pagination.aria.goToNextPage')}
          icon={<ArrowRightIcon />}
          className="rounded-full"
        />
      </div>
    </nav>
  )
}

export default PaginationWithInput
